{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 检查语言output能力"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The autoreload extension is already loaded. To reload it, use:\n", "  %reload_ext autoreload\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Loading checkpoint shards: 100%|██████████| 5/5 [00:00<00:00, 15.29it/s]\n", "Some weights of Qwen2VLRetForConditionalGeneration were not initialized from the model checkpoint at Qwen/Qwen2-VL-7B-Instruct and are newly initialized: ['visual.context_layers.0.attn_factor', 'visual.context_layers.0.cross_attn.kv_proj.bias', 'visual.context_layers.0.cross_attn.kv_proj.weight', 'visual.context_layers.0.cross_attn.proj.bias', 'visual.context_layers.0.cross_attn.proj.weight', 'visual.context_layers.0.cross_attn.q_proj.bias', 'visual.context_layers.0.cross_attn.q_proj.weight', 'visual.context_layers.0.mlp.fc1.bias', 'visual.context_layers.0.mlp.fc1.weight', 'visual.context_layers.0.mlp.fc2.bias', 'visual.context_layers.0.mlp.fc2.weight', 'visual.context_layers.0.mlp_factor', 'visual.context_layers.0.norm1.bias', 'visual.context_layers.0.norm1.weight', 'visual.context_layers.0.norm2.bias', 'visual.context_layers.0.norm2.weight', 'visual.context_layers.1.attn_factor', 'visual.context_layers.1.cross_attn.kv_proj.bias', 'visual.context_layers.1.cross_attn.kv_proj.weight', 'visual.context_layers.1.cross_attn.proj.bias', 'visual.context_layers.1.cross_attn.proj.weight', 'visual.context_layers.1.cross_attn.q_proj.bias', 'visual.context_layers.1.cross_attn.q_proj.weight', 'visual.context_layers.1.mlp.fc1.bias', 'visual.context_layers.1.mlp.fc1.weight', 'visual.context_layers.1.mlp.fc2.bias', 'visual.context_layers.1.mlp.fc2.weight', 'visual.context_layers.1.mlp_factor', 'visual.context_layers.1.norm1.bias', 'visual.context_layers.1.norm1.weight', 'visual.context_layers.1.norm2.bias', 'visual.context_layers.1.norm2.weight', 'visual.context_layers.10.attn_factor', 'visual.context_layers.10.cross_attn.kv_proj.bias', 'visual.context_layers.10.cross_attn.kv_proj.weight', 'visual.context_layers.10.cross_attn.proj.bias', 'visual.context_layers.10.cross_attn.proj.weight', 'visual.context_layers.10.cross_attn.q_proj.bias', 'visual.context_layers.10.cross_attn.q_proj.weight', 'visual.context_layers.10.mlp.fc1.bias', 'visual.context_layers.10.mlp.fc1.weight', 'visual.context_layers.10.mlp.fc2.bias', 'visual.context_layers.10.mlp.fc2.weight', 'visual.context_layers.10.mlp_factor', 'visual.context_layers.10.norm1.bias', 'visual.context_layers.10.norm1.weight', 'visual.context_layers.10.norm2.bias', 'visual.context_layers.10.norm2.weight', 'visual.context_layers.11.attn_factor', 'visual.context_layers.11.cross_attn.kv_proj.bias', 'visual.context_layers.11.cross_attn.kv_proj.weight', 'visual.context_layers.11.cross_attn.proj.bias', 'visual.context_layers.11.cross_attn.proj.weight', 'visual.context_layers.11.cross_attn.q_proj.bias', 'visual.context_layers.11.cross_attn.q_proj.weight', 'visual.context_layers.11.mlp.fc1.bias', 'visual.context_layers.11.mlp.fc1.weight', 'visual.context_layers.11.mlp.fc2.bias', 'visual.context_layers.11.mlp.fc2.weight', 'visual.context_layers.11.mlp_factor', 'visual.context_layers.11.norm1.bias', 'visual.context_layers.11.norm1.weight', 'visual.context_layers.11.norm2.bias', 'visual.context_layers.11.norm2.weight', 'visual.context_layers.12.attn_factor', 'visual.context_layers.12.cross_attn.kv_proj.bias', 'visual.context_layers.12.cross_attn.kv_proj.weight', 'visual.context_layers.12.cross_attn.proj.bias', 'visual.context_layers.12.cross_attn.proj.weight', 'visual.context_layers.12.cross_attn.q_proj.bias', 'visual.context_layers.12.cross_attn.q_proj.weight', 'visual.context_layers.12.mlp.fc1.bias', 'visual.context_layers.12.mlp.fc1.weight', 'visual.context_layers.12.mlp.fc2.bias', 'visual.context_layers.12.mlp.fc2.weight', 'visual.context_layers.12.mlp_factor', 'visual.context_layers.12.norm1.bias', 'visual.context_layers.12.norm1.weight', 'visual.context_layers.12.norm2.bias', 'visual.context_layers.12.norm2.weight', 'visual.context_layers.13.attn_factor', 'visual.context_layers.13.cross_attn.kv_proj.bias', 'visual.context_layers.13.cross_attn.kv_proj.weight', 'visual.context_layers.13.cross_attn.proj.bias', 'visual.context_layers.13.cross_attn.proj.weight', 'visual.context_layers.13.cross_attn.q_proj.bias', 'visual.context_layers.13.cross_attn.q_proj.weight', 'visual.context_layers.13.mlp.fc1.bias', 'visual.context_layers.13.mlp.fc1.weight', 'visual.context_layers.13.mlp.fc2.bias', 'visual.context_layers.13.mlp.fc2.weight', 'visual.context_layers.13.mlp_factor', 'visual.context_layers.13.norm1.bias', 'visual.context_layers.13.norm1.weight', 'visual.context_layers.13.norm2.bias', 'visual.context_layers.13.norm2.weight', 'visual.context_layers.14.attn_factor', 'visual.context_layers.14.cross_attn.kv_proj.bias', 'visual.context_layers.14.cross_attn.kv_proj.weight', 'visual.context_layers.14.cross_attn.proj.bias', 'visual.context_layers.14.cross_attn.proj.weight', 'visual.context_layers.14.cross_attn.q_proj.bias', 'visual.context_layers.14.cross_attn.q_proj.weight', 'visual.context_layers.14.mlp.fc1.bias', 'visual.context_layers.14.mlp.fc1.weight', 'visual.context_layers.14.mlp.fc2.bias', 'visual.context_layers.14.mlp.fc2.weight', 'visual.context_layers.14.mlp_factor', 'visual.context_layers.14.norm1.bias', 'visual.context_layers.14.norm1.weight', 'visual.context_layers.14.norm2.bias', 'visual.context_layers.14.norm2.weight', 'visual.context_layers.15.attn_factor', 'visual.context_layers.15.cross_attn.kv_proj.bias', 'visual.context_layers.15.cross_attn.kv_proj.weight', 'visual.context_layers.15.cross_attn.proj.bias', 'visual.context_layers.15.cross_attn.proj.weight', 'visual.context_layers.15.cross_attn.q_proj.bias', 'visual.context_layers.15.cross_attn.q_proj.weight', 'visual.context_layers.15.mlp.fc1.bias', 'visual.context_layers.15.mlp.fc1.weight', 'visual.context_layers.15.mlp.fc2.bias', 'visual.context_layers.15.mlp.fc2.weight', 'visual.context_layers.15.mlp_factor', 'visual.context_layers.15.norm1.bias', 'visual.context_layers.15.norm1.weight', 'visual.context_layers.15.norm2.bias', 'visual.context_layers.15.norm2.weight', 'visual.context_layers.16.attn_factor', 'visual.context_layers.16.cross_attn.kv_proj.bias', 'visual.context_layers.16.cross_attn.kv_proj.weight', 'visual.context_layers.16.cross_attn.proj.bias', 'visual.context_layers.16.cross_attn.proj.weight', 'visual.context_layers.16.cross_attn.q_proj.bias', 'visual.context_layers.16.cross_attn.q_proj.weight', 'visual.context_layers.16.mlp.fc1.bias', 'visual.context_layers.16.mlp.fc1.weight', 'visual.context_layers.16.mlp.fc2.bias', 'visual.context_layers.16.mlp.fc2.weight', 'visual.context_layers.16.mlp_factor', 'visual.context_layers.16.norm1.bias', 'visual.context_layers.16.norm1.weight', 'visual.context_layers.16.norm2.bias', 'visual.context_layers.16.norm2.weight', 'visual.context_layers.17.attn_factor', 'visual.context_layers.17.cross_attn.kv_proj.bias', 'visual.context_layers.17.cross_attn.kv_proj.weight', 'visual.context_layers.17.cross_attn.proj.bias', 'visual.context_layers.17.cross_attn.proj.weight', 'visual.context_layers.17.cross_attn.q_proj.bias', 'visual.context_layers.17.cross_attn.q_proj.weight', 'visual.context_layers.17.mlp.fc1.bias', 'visual.context_layers.17.mlp.fc1.weight', 'visual.context_layers.17.mlp.fc2.bias', 'visual.context_layers.17.mlp.fc2.weight', 'visual.context_layers.17.mlp_factor', 'visual.context_layers.17.norm1.bias', 'visual.context_layers.17.norm1.weight', 'visual.context_layers.17.norm2.bias', 'visual.context_layers.17.norm2.weight', 'visual.context_layers.18.attn_factor', 'visual.context_layers.18.cross_attn.kv_proj.bias', 'visual.context_layers.18.cross_attn.kv_proj.weight', 'visual.context_layers.18.cross_attn.proj.bias', 'visual.context_layers.18.cross_attn.proj.weight', 'visual.context_layers.18.cross_attn.q_proj.bias', 'visual.context_layers.18.cross_attn.q_proj.weight', 'visual.context_layers.18.mlp.fc1.bias', 'visual.context_layers.18.mlp.fc1.weight', 'visual.context_layers.18.mlp.fc2.bias', 'visual.context_layers.18.mlp.fc2.weight', 'visual.context_layers.18.mlp_factor', 'visual.context_layers.18.norm1.bias', 'visual.context_layers.18.norm1.weight', 'visual.context_layers.18.norm2.bias', 'visual.context_layers.18.norm2.weight', 'visual.context_layers.19.attn_factor', 'visual.context_layers.19.cross_attn.kv_proj.bias', 'visual.context_layers.19.cross_attn.kv_proj.weight', 'visual.context_layers.19.cross_attn.proj.bias', 'visual.context_layers.19.cross_attn.proj.weight', 'visual.context_layers.19.cross_attn.q_proj.bias', 'visual.context_layers.19.cross_attn.q_proj.weight', 'visual.context_layers.19.mlp.fc1.bias', 'visual.context_layers.19.mlp.fc1.weight', 'visual.context_layers.19.mlp.fc2.bias', 'visual.context_layers.19.mlp.fc2.weight', 'visual.context_layers.19.mlp_factor', 'visual.context_layers.19.norm1.bias', 'visual.context_layers.19.norm1.weight', 'visual.context_layers.19.norm2.bias', 'visual.context_layers.19.norm2.weight', 'visual.context_layers.2.attn_factor', 'visual.context_layers.2.cross_attn.kv_proj.bias', 'visual.context_layers.2.cross_attn.kv_proj.weight', 'visual.context_layers.2.cross_attn.proj.bias', 'visual.context_layers.2.cross_attn.proj.weight', 'visual.context_layers.2.cross_attn.q_proj.bias', 'visual.context_layers.2.cross_attn.q_proj.weight', 'visual.context_layers.2.mlp.fc1.bias', 'visual.context_layers.2.mlp.fc1.weight', 'visual.context_layers.2.mlp.fc2.bias', 'visual.context_layers.2.mlp.fc2.weight', 'visual.context_layers.2.mlp_factor', 'visual.context_layers.2.norm1.bias', 'visual.context_layers.2.norm1.weight', 'visual.context_layers.2.norm2.bias', 'visual.context_layers.2.norm2.weight', 'visual.context_layers.20.attn_factor', 'visual.context_layers.20.cross_attn.kv_proj.bias', 'visual.context_layers.20.cross_attn.kv_proj.weight', 'visual.context_layers.20.cross_attn.proj.bias', 'visual.context_layers.20.cross_attn.proj.weight', 'visual.context_layers.20.cross_attn.q_proj.bias', 'visual.context_layers.20.cross_attn.q_proj.weight', 'visual.context_layers.20.mlp.fc1.bias', 'visual.context_layers.20.mlp.fc1.weight', 'visual.context_layers.20.mlp.fc2.bias', 'visual.context_layers.20.mlp.fc2.weight', 'visual.context_layers.20.mlp_factor', 'visual.context_layers.20.norm1.bias', 'visual.context_layers.20.norm1.weight', 'visual.context_layers.20.norm2.bias', 'visual.context_layers.20.norm2.weight', 'visual.context_layers.21.attn_factor', 'visual.context_layers.21.cross_attn.kv_proj.bias', 'visual.context_layers.21.cross_attn.kv_proj.weight', 'visual.context_layers.21.cross_attn.proj.bias', 'visual.context_layers.21.cross_attn.proj.weight', 'visual.context_layers.21.cross_attn.q_proj.bias', 'visual.context_layers.21.cross_attn.q_proj.weight', 'visual.context_layers.21.mlp.fc1.bias', 'visual.context_layers.21.mlp.fc1.weight', 'visual.context_layers.21.mlp.fc2.bias', 'visual.context_layers.21.mlp.fc2.weight', 'visual.context_layers.21.mlp_factor', 'visual.context_layers.21.norm1.bias', 'visual.context_layers.21.norm1.weight', 'visual.context_layers.21.norm2.bias', 'visual.context_layers.21.norm2.weight', 'visual.context_layers.22.attn_factor', 'visual.context_layers.22.cross_attn.kv_proj.bias', 'visual.context_layers.22.cross_attn.kv_proj.weight', 'visual.context_layers.22.cross_attn.proj.bias', 'visual.context_layers.22.cross_attn.proj.weight', 'visual.context_layers.22.cross_attn.q_proj.bias', 'visual.context_layers.22.cross_attn.q_proj.weight', 'visual.context_layers.22.mlp.fc1.bias', 'visual.context_layers.22.mlp.fc1.weight', 'visual.context_layers.22.mlp.fc2.bias', 'visual.context_layers.22.mlp.fc2.weight', 'visual.context_layers.22.mlp_factor', 'visual.context_layers.22.norm1.bias', 'visual.context_layers.22.norm1.weight', 'visual.context_layers.22.norm2.bias', 'visual.context_layers.22.norm2.weight', 'visual.context_layers.23.attn_factor', 'visual.context_layers.23.cross_attn.kv_proj.bias', 'visual.context_layers.23.cross_attn.kv_proj.weight', 'visual.context_layers.23.cross_attn.proj.bias', 'visual.context_layers.23.cross_attn.proj.weight', 'visual.context_layers.23.cross_attn.q_proj.bias', 'visual.context_layers.23.cross_attn.q_proj.weight', 'visual.context_layers.23.mlp.fc1.bias', 'visual.context_layers.23.mlp.fc1.weight', 'visual.context_layers.23.mlp.fc2.bias', 'visual.context_layers.23.mlp.fc2.weight', 'visual.context_layers.23.mlp_factor', 'visual.context_layers.23.norm1.bias', 'visual.context_layers.23.norm1.weight', 'visual.context_layers.23.norm2.bias', 'visual.context_layers.23.norm2.weight', 'visual.context_layers.24.attn_factor', 'visual.context_layers.24.cross_attn.kv_proj.bias', 'visual.context_layers.24.cross_attn.kv_proj.weight', 'visual.context_layers.24.cross_attn.proj.bias', 'visual.context_layers.24.cross_attn.proj.weight', 'visual.context_layers.24.cross_attn.q_proj.bias', 'visual.context_layers.24.cross_attn.q_proj.weight', 'visual.context_layers.24.mlp.fc1.bias', 'visual.context_layers.24.mlp.fc1.weight', 'visual.context_layers.24.mlp.fc2.bias', 'visual.context_layers.24.mlp.fc2.weight', 'visual.context_layers.24.mlp_factor', 'visual.context_layers.24.norm1.bias', 'visual.context_layers.24.norm1.weight', 'visual.context_layers.24.norm2.bias', 'visual.context_layers.24.norm2.weight', 'visual.context_layers.25.attn_factor', 'visual.context_layers.25.cross_attn.kv_proj.bias', 'visual.context_layers.25.cross_attn.kv_proj.weight', 'visual.context_layers.25.cross_attn.proj.bias', 'visual.context_layers.25.cross_attn.proj.weight', 'visual.context_layers.25.cross_attn.q_proj.bias', 'visual.context_layers.25.cross_attn.q_proj.weight', 'visual.context_layers.25.mlp.fc1.bias', 'visual.context_layers.25.mlp.fc1.weight', 'visual.context_layers.25.mlp.fc2.bias', 'visual.context_layers.25.mlp.fc2.weight', 'visual.context_layers.25.mlp_factor', 'visual.context_layers.25.norm1.bias', 'visual.context_layers.25.norm1.weight', 'visual.context_layers.25.norm2.bias', 'visual.context_layers.25.norm2.weight', 'visual.context_layers.26.attn_factor', 'visual.context_layers.26.cross_attn.kv_proj.bias', 'visual.context_layers.26.cross_attn.kv_proj.weight', 'visual.context_layers.26.cross_attn.proj.bias', 'visual.context_layers.26.cross_attn.proj.weight', 'visual.context_layers.26.cross_attn.q_proj.bias', 'visual.context_layers.26.cross_attn.q_proj.weight', 'visual.context_layers.26.mlp.fc1.bias', 'visual.context_layers.26.mlp.fc1.weight', 'visual.context_layers.26.mlp.fc2.bias', 'visual.context_layers.26.mlp.fc2.weight', 'visual.context_layers.26.mlp_factor', 'visual.context_layers.26.norm1.bias', 'visual.context_layers.26.norm1.weight', 'visual.context_layers.26.norm2.bias', 'visual.context_layers.26.norm2.weight', 'visual.context_layers.27.attn_factor', 'visual.context_layers.27.cross_attn.kv_proj.bias', 'visual.context_layers.27.cross_attn.kv_proj.weight', 'visual.context_layers.27.cross_attn.proj.bias', 'visual.context_layers.27.cross_attn.proj.weight', 'visual.context_layers.27.cross_attn.q_proj.bias', 'visual.context_layers.27.cross_attn.q_proj.weight', 'visual.context_layers.27.mlp.fc1.bias', 'visual.context_layers.27.mlp.fc1.weight', 'visual.context_layers.27.mlp.fc2.bias', 'visual.context_layers.27.mlp.fc2.weight', 'visual.context_layers.27.mlp_factor', 'visual.context_layers.27.norm1.bias', 'visual.context_layers.27.norm1.weight', 'visual.context_layers.27.norm2.bias', 'visual.context_layers.27.norm2.weight', 'visual.context_layers.28.attn_factor', 'visual.context_layers.28.cross_attn.kv_proj.bias', 'visual.context_layers.28.cross_attn.kv_proj.weight', 'visual.context_layers.28.cross_attn.proj.bias', 'visual.context_layers.28.cross_attn.proj.weight', 'visual.context_layers.28.cross_attn.q_proj.bias', 'visual.context_layers.28.cross_attn.q_proj.weight', 'visual.context_layers.28.mlp.fc1.bias', 'visual.context_layers.28.mlp.fc1.weight', 'visual.context_layers.28.mlp.fc2.bias', 'visual.context_layers.28.mlp.fc2.weight', 'visual.context_layers.28.mlp_factor', 'visual.context_layers.28.norm1.bias', 'visual.context_layers.28.norm1.weight', 'visual.context_layers.28.norm2.bias', 'visual.context_layers.28.norm2.weight', 'visual.context_layers.29.attn_factor', 'visual.context_layers.29.cross_attn.kv_proj.bias', 'visual.context_layers.29.cross_attn.kv_proj.weight', 'visual.context_layers.29.cross_attn.proj.bias', 'visual.context_layers.29.cross_attn.proj.weight', 'visual.context_layers.29.cross_attn.q_proj.bias', 'visual.context_layers.29.cross_attn.q_proj.weight', 'visual.context_layers.29.mlp.fc1.bias', 'visual.context_layers.29.mlp.fc1.weight', 'visual.context_layers.29.mlp.fc2.bias', 'visual.context_layers.29.mlp.fc2.weight', 'visual.context_layers.29.mlp_factor', 'visual.context_layers.29.norm1.bias', 'visual.context_layers.29.norm1.weight', 'visual.context_layers.29.norm2.bias', 'visual.context_layers.29.norm2.weight', 'visual.context_layers.3.attn_factor', 'visual.context_layers.3.cross_attn.kv_proj.bias', 'visual.context_layers.3.cross_attn.kv_proj.weight', 'visual.context_layers.3.cross_attn.proj.bias', 'visual.context_layers.3.cross_attn.proj.weight', 'visual.context_layers.3.cross_attn.q_proj.bias', 'visual.context_layers.3.cross_attn.q_proj.weight', 'visual.context_layers.3.mlp.fc1.bias', 'visual.context_layers.3.mlp.fc1.weight', 'visual.context_layers.3.mlp.fc2.bias', 'visual.context_layers.3.mlp.fc2.weight', 'visual.context_layers.3.mlp_factor', 'visual.context_layers.3.norm1.bias', 'visual.context_layers.3.norm1.weight', 'visual.context_layers.3.norm2.bias', 'visual.context_layers.3.norm2.weight', 'visual.context_layers.30.attn_factor', 'visual.context_layers.30.cross_attn.kv_proj.bias', 'visual.context_layers.30.cross_attn.kv_proj.weight', 'visual.context_layers.30.cross_attn.proj.bias', 'visual.context_layers.30.cross_attn.proj.weight', 'visual.context_layers.30.cross_attn.q_proj.bias', 'visual.context_layers.30.cross_attn.q_proj.weight', 'visual.context_layers.30.mlp.fc1.bias', 'visual.context_layers.30.mlp.fc1.weight', 'visual.context_layers.30.mlp.fc2.bias', 'visual.context_layers.30.mlp.fc2.weight', 'visual.context_layers.30.mlp_factor', 'visual.context_layers.30.norm1.bias', 'visual.context_layers.30.norm1.weight', 'visual.context_layers.30.norm2.bias', 'visual.context_layers.30.norm2.weight', 'visual.context_layers.31.attn_factor', 'visual.context_layers.31.cross_attn.kv_proj.bias', 'visual.context_layers.31.cross_attn.kv_proj.weight', 'visual.context_layers.31.cross_attn.proj.bias', 'visual.context_layers.31.cross_attn.proj.weight', 'visual.context_layers.31.cross_attn.q_proj.bias', 'visual.context_layers.31.cross_attn.q_proj.weight', 'visual.context_layers.31.mlp.fc1.bias', 'visual.context_layers.31.mlp.fc1.weight', 'visual.context_layers.31.mlp.fc2.bias', 'visual.context_layers.31.mlp.fc2.weight', 'visual.context_layers.31.mlp_factor', 'visual.context_layers.31.norm1.bias', 'visual.context_layers.31.norm1.weight', 'visual.context_layers.31.norm2.bias', 'visual.context_layers.31.norm2.weight', 'visual.context_layers.4.attn_factor', 'visual.context_layers.4.cross_attn.kv_proj.bias', 'visual.context_layers.4.cross_attn.kv_proj.weight', 'visual.context_layers.4.cross_attn.proj.bias', 'visual.context_layers.4.cross_attn.proj.weight', 'visual.context_layers.4.cross_attn.q_proj.bias', 'visual.context_layers.4.cross_attn.q_proj.weight', 'visual.context_layers.4.mlp.fc1.bias', 'visual.context_layers.4.mlp.fc1.weight', 'visual.context_layers.4.mlp.fc2.bias', 'visual.context_layers.4.mlp.fc2.weight', 'visual.context_layers.4.mlp_factor', 'visual.context_layers.4.norm1.bias', 'visual.context_layers.4.norm1.weight', 'visual.context_layers.4.norm2.bias', 'visual.context_layers.4.norm2.weight', 'visual.context_layers.5.attn_factor', 'visual.context_layers.5.cross_attn.kv_proj.bias', 'visual.context_layers.5.cross_attn.kv_proj.weight', 'visual.context_layers.5.cross_attn.proj.bias', 'visual.context_layers.5.cross_attn.proj.weight', 'visual.context_layers.5.cross_attn.q_proj.bias', 'visual.context_layers.5.cross_attn.q_proj.weight', 'visual.context_layers.5.mlp.fc1.bias', 'visual.context_layers.5.mlp.fc1.weight', 'visual.context_layers.5.mlp.fc2.bias', 'visual.context_layers.5.mlp.fc2.weight', 'visual.context_layers.5.mlp_factor', 'visual.context_layers.5.norm1.bias', 'visual.context_layers.5.norm1.weight', 'visual.context_layers.5.norm2.bias', 'visual.context_layers.5.norm2.weight', 'visual.context_layers.6.attn_factor', 'visual.context_layers.6.cross_attn.kv_proj.bias', 'visual.context_layers.6.cross_attn.kv_proj.weight', 'visual.context_layers.6.cross_attn.proj.bias', 'visual.context_layers.6.cross_attn.proj.weight', 'visual.context_layers.6.cross_attn.q_proj.bias', 'visual.context_layers.6.cross_attn.q_proj.weight', 'visual.context_layers.6.mlp.fc1.bias', 'visual.context_layers.6.mlp.fc1.weight', 'visual.context_layers.6.mlp.fc2.bias', 'visual.context_layers.6.mlp.fc2.weight', 'visual.context_layers.6.mlp_factor', 'visual.context_layers.6.norm1.bias', 'visual.context_layers.6.norm1.weight', 'visual.context_layers.6.norm2.bias', 'visual.context_layers.6.norm2.weight', 'visual.context_layers.7.attn_factor', 'visual.context_layers.7.cross_attn.kv_proj.bias', 'visual.context_layers.7.cross_attn.kv_proj.weight', 'visual.context_layers.7.cross_attn.proj.bias', 'visual.context_layers.7.cross_attn.proj.weight', 'visual.context_layers.7.cross_attn.q_proj.bias', 'visual.context_layers.7.cross_attn.q_proj.weight', 'visual.context_layers.7.mlp.fc1.bias', 'visual.context_layers.7.mlp.fc1.weight', 'visual.context_layers.7.mlp.fc2.bias', 'visual.context_layers.7.mlp.fc2.weight', 'visual.context_layers.7.mlp_factor', 'visual.context_layers.7.norm1.bias', 'visual.context_layers.7.norm1.weight', 'visual.context_layers.7.norm2.bias', 'visual.context_layers.7.norm2.weight', 'visual.context_layers.8.attn_factor', 'visual.context_layers.8.cross_attn.kv_proj.bias', 'visual.context_layers.8.cross_attn.kv_proj.weight', 'visual.context_layers.8.cross_attn.proj.bias', 'visual.context_layers.8.cross_attn.proj.weight', 'visual.context_layers.8.cross_attn.q_proj.bias', 'visual.context_layers.8.cross_attn.q_proj.weight', 'visual.context_layers.8.mlp.fc1.bias', 'visual.context_layers.8.mlp.fc1.weight', 'visual.context_layers.8.mlp.fc2.bias', 'visual.context_layers.8.mlp.fc2.weight', 'visual.context_layers.8.mlp_factor', 'visual.context_layers.8.norm1.bias', 'visual.context_layers.8.norm1.weight', 'visual.context_layers.8.norm2.bias', 'visual.context_layers.8.norm2.weight', 'visual.context_layers.9.attn_factor', 'visual.context_layers.9.cross_attn.kv_proj.bias', 'visual.context_layers.9.cross_attn.kv_proj.weight', 'visual.context_layers.9.cross_attn.proj.bias', 'visual.context_layers.9.cross_attn.proj.weight', 'visual.context_layers.9.cross_attn.q_proj.bias', 'visual.context_layers.9.cross_attn.q_proj.weight', 'visual.context_layers.9.mlp.fc1.bias', 'visual.context_layers.9.mlp.fc1.weight', 'visual.context_layers.9.mlp.fc2.bias', 'visual.context_layers.9.mlp.fc2.weight', 'visual.context_layers.9.mlp_factor', 'visual.context_layers.9.norm1.bias', 'visual.context_layers.9.norm1.weight', 'visual.context_layers.9.norm2.bias', 'visual.context_layers.9.norm2.weight', 'visual.ctx_merger.ln_q.bias', 'visual.ctx_merger.ln_q.weight', 'visual.ctx_merger.mlp.0.bias', 'visual.ctx_merger.mlp.0.weight', 'visual.ctx_merger.mlp.2.bias', 'visual.ctx_merger.mlp.2.weight']\n", "You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import os\n", "import sys\n", "# NOTE enable this in single script\n", "# current_file_path = os.path.dirname(os.path.abspath(__file__))\n", "# module_path = os.path.join(current_file_path, \"../..\")\n", "# sys.path.append(module_path)\n", "from dataclasses import asdict\n", "import math\n", "from pathlib import Path\n", "from typing import List, Optional\n", "import yaml\n", "import debugpy\n", "\n", "from peft import LoraConfig, get_peft_model, prepare_model_for_kbit_training\n", "import torch\n", "import transformers\n", "from transformers import Trainer, is_datasets_available\n", "import datasets\n", "from transformers.integrations import deepspeed\n", "from torch.utils.data import Dataset, ConcatDataset, WeightedRandomSampler, RandomSampler, DataLoader\n", "\n", "from transformers import AutoProcessor, AutoTokenizer\n", "from models.qwen2_vl import Qwen2VLRetForConditionalGeneration\n", "\n", "from arguments import ModelArguments, DataArguments, TrainingArguments, LoraArguments\n", "from collators import COLLATORS\n", "from dataset.datasets_mbeir import LazySupervisedDataset, MbeirLanguageDataset\n", "from dataset.datasets_xhs import XHSDataset\n", "from dataset.datasets_dam import DAMDataset\n", "# from dataset.datasets_mmeb import MMEBDataset\n", "from loaders import LOADERS\n", "from supported_models import MODULE_KEYWORDS\n", "from utils import (\n", "    rank0_print\n", ")\n", "    \n", "# model = Qwen2VLRetForConditionalGeneration.from_pretrained(\"./checkpoints/qwen2-vl-2b_DAM_cp\", attn_implementation= \"flash_attention_2\", torch_dtype=torch.bfloat16, low_cpu_mem_usage=True).cuda()\n", "# model = Qwen2VLRetForConditionalGeneration.from_pretrained(\"./checkpoints/Qwen2-VL-2B-cp\", attn_implementation= \"flash_attention_2\", torch_dtype=torch.bfloat16, low_cpu_mem_usage=True).cuda()\n", "# model = Qwen2VLRetForConditionalGeneration.from_pretrained(\"/mnt/tidal-alsh01/dataset/mmeb/Qwen2-VL-2B-Instruct\", attn_implementation= \"flash_attention_2\", torch_dtype=torch.bfloat16, low_cpu_mem_usage=True).cuda()\n", "model = Qwen2VLRetForConditionalGeneration.from_pretrained(\"Qwen/Qwen2-VL-7B-Instruct\", attn_implementation= \"flash_attention_2\", torch_dtype=torch.bfloat16, low_cpu_mem_usage=True).cuda()\n", "# processor = AutoProcessor.from_pretrained(\"Qwen/Qwen2.5-VL-7B-Instruct\")\n", "\n", "# processor = AutoProcessor.from_pretrained(\"./checkpoints/qwen2-vl-2b_DAM_cp\")\n", "# processor = AutoProcessor.from_pretrained(\"/mnt/tidal-alsh01/dataset/mmeb/Qwen2-VL-2B-Instruct\")\n", "processor = AutoProcessor.from_pretrained(\"Qwen/Qwen2-VL-7B-Instruct\")\n", "# tokenizer = processor.tokenizer\n", "tokenizer = AutoTokenizer.from_pretrained(\"Qwen/Qwen2-VL-7B-Instruct\")\n", "tokenizer.padding_side  = 'left'\n", "\n", "# data_collator = COLLATORS['qwen2-vl-7b'](\n", "data_collator = COLLATORS['qwen2-vl-7b'](\n", "    tokenizer=tokenizer,\n", "    processor=processor,\n", ")\n"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [], "source": ["data_collator = COLLATORS['qwen2-vl-7b'](\n", "    tokenizer=tokenizer,\n", "    processor=processor,\n", ")"]}, {"cell_type": "code", "execution_count": 49, "metadata": {}, "outputs": [], "source": ["\n", "device = 'cuda:0'\n", "\n", "def tensors_to_device(data, device, dtype=model.dtype):\n", "    for key in data.keys():\n", "        if isinstance(data[key], torch.Tensor):\n", "            if key == 'pixel_values':\n", "                data[key] = data[key].to(device).to(dtype)\n", "            else:\n", "                data[key] = data[key].to(device)\n", "    return data \n", "\n", "\n", "model.to(device)\n", "model.eval()\n", "model.config.emb_token_ids = [-777]"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [], "source": ["from dataset.datasets_dam import DAMDataset\n", "dam_dataset = DAMDataset(\n", "    data_path=\"/mnt/tidal-alsh01/dataset/mmeb/describe-anything-data\",\n", "    max_length=10000,\n", "    mode='crop', inference=True\n", ")"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [], "source": ["\n", "\n", "\n", "dataloader = DataLoader(dam_dataset, batch_size=1, num_workers=0, shuffle=False, collate_fn=data_collator)\n", "dataloader = iter(dataloader)\n", "\n", "\n", "# TODO : 跑之前要把dam ds返回的改成1\n", "datalist = [d for d,i in zip(dataloader, range(10))]\n"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [], "source": ["generation_kwargs = {\n", "    'max_new_tokens': 1024,\n", "    'do_sample': True,           # 启用采样以获得更多样化的输出\n", "    'temperature': 0.7,          # 控制随机性\n", "    'top_p': 0.9,               # 核采样\n", "    'top_k': 50,                # Top-k采样\n", "    'pad_token_id': tokenizer.eos_token_id,\n", "    'eos_token_id': tokenizer.eos_token_id,\n", "    'repetition_penalty': 1.1,  # 减少重复\n", "}\n", "with torch.no_grad():\n", "    for i, data in enumerate(datalist):\n", "        # data = next(dataloader)\n", "        batch = tensors_to_device(data, device)\n", "        output = model.generate(**batch, **generation_kwargs)\n", "        image_generated_ids = output\n", "        image_input_length = batch['input_ids'].shape[1]\n", "        image_generated_only_ids = image_generated_ids[:, image_input_length:]\n", "\n", "        # 解码生成的 token IDs 为文本\n", "        # `skip_special_tokens=True` 会移除像 <|endoftext|> 这样的特殊标记\n", "        image_decoded_outputs = tokenizer.batch_decode(image_generated_ids, skip_special_tokens=True)\n", "        print(\"@decode inputs:\" ,image_decoded_outputs)"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"data": {"text/plain": ["['system\\nYou are a helpful assistant.\\nuser\\n\\nDescribe the image in detail.\\n']"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["tokenizer.batch_decode(datalist[0]['input_ids'], skip_special_tokens=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import pycocotools\n", "from PIL import Image\n", "import pickle\n", "\n", "def counts_to_mask(maskrle):\n", "    return np.array(pycocotools.mask.decode(maskrle), dtype=np.float32)\n", "\n", "def visualize_mask_on_image_pil(original_pil, binary_mask_np, \n", "                                color=(255, 0, 0), alpha_percent=50):\n", "    height, width = binary_mask_np.shape\n", "    if original_pil.size != (width, height):\n", "        print(f\"Warning: Mask size ({width},{height}) and image size ({original_pil.size}) differ.\")\n", "    alpha_value = int((alpha_percent / 100.0) * 255)\n", "    colored_mask_pil = Image.new(\"RGBA\", original_pil.size, (0, 0, 0, 0))\n", "    mask_rgba_np = np.zeros((height, width, 4), dtype=np.uint8)\n", "    \n", "    mask_indices = binary_mask_np == 1\n", "    mask_rgba_np[mask_indices] = list(color) + [alpha_value]\n", "\n", "    colored_mask_pil_from_np = Image.fromarray(mask_rgba_np, \"RGBA\")\n", "    original_pil.putalpha(255)\n", "    overlaid_image_pil = Image.alpha_composite(original_pil, colored_mask_pil_from_np)    \n", "    return overlaid_image_pil\n", "\n", "def mask2box(mask):\n", "    box = None\n", "    pos = np.where(mask > 0)\n", "    height, width = mask.shape\n", "\n", "    if pos[0].size > 0 and pos[1].size > 0:\n", "        x_min = np.min(pos[1]) / width\n", "        x_max = np.max(pos[1]) / width\n", "        y_min = np.min(pos[0]) / height\n", "        y_max = np.max(pos[0]) / height\n", "        box = [x_min, y_min, x_max, y_max]\n", "    return box\n", "\n", "\n", "dataset = dam_dataset.dataset['SAM']['train']\n", "idx = (9,0)\n", "anno = pickle.loads(dataset[idx[0]]['pickle'])\n", "print(anno[idx[1]])\n", "mask = counts_to_mask(anno[idx[1]]['mask_rle'])\n", "box = mask2box(mask)\n", "# print(anno[idx[1]]['category'])\n", "# print(anno[idx[1]]['caption'])\n", "img = dataset[idx[0]]['jpg']\n", "print(box)\n", "width, height = img.size\n", "x0 = width * box[0]\n", "y0 = height * box[1]\n", "x1 = width * box[2]\n", "y1 = height * box[3]\n", "\n", "img.crop((x0, y0, x1, y1)).show()\n", "visualize_mask_on_image_pil(img, mask)"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["(1500, 2060)"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset[idx[0]]['jpg'].size"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# 测试xhs的描述"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from dataset.datasets_xhs import XHSDataset\n", "from dataset.datasets_mbeir import LazySupervisedDataset, MbeirLanguageDataset\n", "mnt = \"tidalfs-hssh01\"\n", "xhs_dataset = XHSDataset(\n", "    query_data_path=f\"/mnt/{mnt}/dataset/mmeb/M-BEIR/query/train/mbeir_xhsnote_task7_train.jsonl\",\n", "    cand_pool_path=f\"/mnt/{mnt}/dataset/mmeb/M-BEIR/cand_pool/local/mbeir_xhsnote_task7_cand_pool.jsonl\",\n", "    instructions_path=f\"/mnt/{mnt}/dataset/mmeb/M-BEIR/instructions/query_instructions.tsv\",\n", "    image_path_prefix=f\"/mnt/{mnt}/dataset/M-BEIR\",\n", "    tokenizer=tokenizer \n", ")\n"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[([{'role': 'user', 'content': [{'type': 'image', 'image': '/mnt/tidalfs-hssh01/dataset/mmeb/xhs_data/note_data/20250304/images/1040g0083189pmk643k0g5n7rfq898st4v5d9jmg.jpg', 'box': [0.35006, 0.76561, 0.59137, 0.96301]}, {'type': 'text', 'text': '\\nDescribe the image in detail.'}]}], [{'role': 'user', 'content': [{'type': 'image', 'image': '/mnt/tidalfs-hssh01/dataset/mmeb/xhs_data/note_data/20250304/images/1040g2sg313udgnsoh87g5oppbr9m5fp8hrmo6n8.jpg', 'box': [0.46301, 0.3773, 0.92572, 0.75576]}, {'type': 'text', 'text': '\\nDescribe the image in detail.'}]}])]\n", "[([{'role': 'user', 'content': [{'type': 'image', 'image': '/mnt/tidalfs-hssh01/dataset/mmeb/xhs_data/note_data/20250304/images/active_search_1040g0mg3181i0slljk1g49uktbgs606gp305c58.jpg', 'box': [0.3, 0.31, 0.9, 0.56]}, {'type': 'text', 'text': '\\nDescribe the image in detail.'}]}], [{'role': 'user', 'content': [{'type': 'image', 'image': '/mnt/tidalfs-hssh01/dataset/mmeb/xhs_data/note_data/20250304/images/1040g2sg3133v933038105n41s1blj9dmalkc2uo.jpg', 'box': [0.19748, 0.39252, 0.88644, 0.80143]}, {'type': 'text', 'text': '\\nDescribe the image in detail.'}]}])]\n", "[([{'role': 'user', 'content': [{'type': 'image', 'image': '/mnt/tidalfs-hssh01/dataset/mmeb/xhs_data/note_data/20250304/images/active_search_1040g0mg315bp74ahgu105ndumvp08tmf6q06guo.jpg', 'box': [0.18, 0.36, 0.9, 0.67]}, {'type': 'text', 'text': '\\nDescribe the image in detail.'}]}], [{'role': 'user', 'content': [{'type': 'image', 'image': '/mnt/tidalfs-hssh01/dataset/mmeb/xhs_data/note_data/20250304/images/1040g00830nhlnkml7e004a0murrb9h3tqe1oimo.jpg', 'box': [0.01178, 0.28508, 0.98734, 0.96398]}, {'type': 'text', 'text': '\\nDescribe the image in detail.'}]}])]\n", "@decode inputs: ['system\\nYou are a helpful assistant.\\nuser\\n\\nDescribe the image in detail.\\nassistant\\nA black, long-sleeved shirt with a fitted silhouette and a high neckline. The fabric appears to be smooth and slightly shiny, with a subtle sheen that reflects light. The sleeves are long and extend past the elbows, ending in a clean hem. The shirt is worn ', 'system\\nYou are a helpful assistant.\\nuser\\n\\nDescribe the image in detail.\\nassistant\\nA black, long-sleeved shirt with a fitted silhouette and a crew neckline. The fabric appears to be smooth and slightly stretchy, with a subtle sheen. The sleeves are long and end at the wrists, which are not visible in the image. The shirt is worn over a blac']\n"]}, {"name": "stdout", "output_type": "stream", "text": ["@decode inputs: ['system\\nYou are a helpful assistant.\\nuser\\n\\nDescribe the image in detail.\\nassistant\\nA black, long-sleeved dress with a fitted bodice and a flared skirt. The dress features a high neckline and a zipper closure on the left side. The fabric appears to be smooth and slightly shiny, with a subtle texture that suggests a satin-like material. Th', 'system\\nYou are a helpful assistant.\\nuser\\n\\nDescribe the image in detail.\\nassistant\\nA black dress with a fitted bodice and a flared skirt. The bodice features a deep V-neckline and a high waistline, accentuated by a belt with a decorative buckle. The skirt is adorned with a pattern of white and silver metallic flowers and leaves, creating']\n"]}], "source": ["\n", "data_collator = COLLATORS['qwen2_5-vl-7b'](\n", "    tokenizer=tokenizer,\n", "    processor=processor,\n", ")\n", "\n", "dataloader = DataLoader(xhs_dataset, batch_size=1, num_workers=0, shuffle=False, collate_fn=data_collator)\n", "dataloader = iter(dataloader)\n", "\n", "\n", "# TODO : 跑之前要把dataset_xhs的prompt改成描述类型的；吧mbeir_dataset中get random poscand改成固定的\n", "datalist = [d for d,i in zip(dataloader, range(2))]\n", "\n", "with torch.no_grad():\n", "    for i, data in enumerate(datalist):\n", "        # data = next(dataloader)\n", "        batch = tensors_to_device(data, device)\n", "        output = model.generate(**batch, max_new_tokens=1024)\n", "        image_generated_ids = output\n", "        image_input_length = batch['input_ids'].shape[1]\n", "        image_generated_only_ids = image_generated_ids[:, image_input_length:]\n", "\n", "        # 解码生成的 token IDs 为文本\n", "        # `skip_special_tokens=True` 会移除像 <|endoftext|> 这样的特殊标记\n", "        image_decoded_outputs = tokenizer.batch_decode(image_generated_ids, skip_special_tokens=True)\n", "        print(\"@decode inputs:\" ,image_decoded_outputs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["手动打开图片并且渲染路径"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["xhs_dataset[0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from PIL import Image\n", "\n", "Image.open(\n", "'/mnt/tidalfs-hssh01/dataset/mmeb/xhs_data/note_data/20250304/images/1040g2sg3133v933038105n41s1blj9dmalkc2uo.jpg'\n", ").show()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 4}