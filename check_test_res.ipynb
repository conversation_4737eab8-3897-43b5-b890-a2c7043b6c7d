{"cells": [{"cell_type": "code", "execution_count": 1, "id": "cc078623", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/usr/local/lib/python3.10/dist-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "/usr/local/lib/python3.10/dist-packages/transformers/utils/hub.py:105: FutureWarning: Using `TRANSFORMERS_CACHE` is deprecated and will be removed in v5 of Transformers. Use `HF_HOME` instead.\n", "  warnings.warn(\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import json\n", "from transformers import AutoProcessor\n", "import sys \n", "import os \n", "# current_file_path = os.path.dirname(os.path.abspath(__file__))\n", "# module_path = os.path.join(current_file_path, \"../\")\n", "# sys.path.append(module_path)\n", "# from models.qwen2_5_vl import Qwen2VLRetForConditionalGeneration\n", "import torch \n", "import argparse\n", "from dataset.datasets_mbeir import QueryDataset, CandidateDataset\n", "from collators.mbeir_eval import MbeirQueryDataCollator, MbeirCandidateDataCollator\n", "from torch.utils.data import DataLoader \n", "import torch.nn.functional as F \n", "import numpy as np\n", "DATASET_QUERY_NUM_UPPER_BOUND = 500000\n", "DATASET_CAN_NUM_UPPER_BOUND = 10000000\n", "\n", "NUM_QUERIES = 30\n", "NUM_CANDIDATES_FOR_POOL = 300 # Number of candidates to use in the evaluation pool for simulated retrieval\n", "MAX_RETRIEVED_PER_QUERY = 50 # For simulated retrieval and recall calculation up to K=50\n"]}, {"cell_type": "code", "execution_count": 5, "id": "2ecde0aa", "metadata": {}, "outputs": [], "source": ["\n", "def unhash_qid(hashed_qid):\n", "    dataset_id = hashed_qid // DATASET_QUERY_NUM_UPPER_BOUND\n", "    data_within_id = hashed_qid % DATASET_QUERY_NUM_UPPER_BOUND\n", "    return f\"{dataset_id}:{data_within_id}\"\n", "\n", "def unhash_did(hashed_did):\n", "    dataset_id = hashed_did // DATASET_CAN_NUM_UPPER_BOUND\n", "    data_within_id = hashed_did % DATASET_CAN_NUM_UPPER_BOUND\n", "    return f\"{dataset_id}:{data_within_id}\"\n", "\n", "def load_qrel(filename):\n", "    qrel = {}\n", "    qid_to_taskid = {}\n", "    with open(filename, \"r\") as f:\n", "        for line in f:\n", "            query_id, _, doc_id, relevance_score, task_id = line.strip().split()\n", "            if int(relevance_score) > 0:  # Assuming only positive relevance scores indicate relevant documents\n", "                if query_id not in qrel:\n", "                    qrel[query_id] = []\n", "                qrel[query_id].append(doc_id)\n", "                if query_id not in qid_to_taskid:\n", "                    qid_to_taskid[query_id] = task_id\n", "    print(f\"Retriever: Loaded {len(qrel)} queries from {filename}\")\n", "    print(\n", "        f\"Retriever: Average number of relevant documents per query: {sum(len(v) for v in qrel.values()) / len(qrel):.2f}\"\n", "    )\n", "    return qrel, qid_to_taskid\n", "\n", "def compute_recall_at_k(relevant_docs, retrieved_indices, k):\n", "    if not relevant_docs:\n", "        return 0.0 # Return 0 if there are no relevant documents\n", "\n", "    # Get the set of indices for the top k retrieved documents\n", "    top_k_retrieved_indices_set = set(retrieved_indices[:k])\n", "\n", "    # Convert the relevant documents to a set\n", "    relevant_docs_set = set(relevant_docs)\n", "\n", "    # Check if there is an intersection between relevant docs and top k retrieved docs\n", "    # If there is, we return 1, indicating successful retrieval; otherwise, we return 0\n", "    if relevant_docs_set.intersection(top_k_retrieved_indices_set):\n", "        return 1.0\n", "    else:\n", "        return 0.0\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "996a17f4", "metadata": {}, "outputs": [], "source": ["DS_ID = 12\n", "TASKID = \"vismin_task6\"\n", "IMG_PATH = \"/mbeir_images/vismin_images\"\n", "\n", "DS_ID = 13\n", "TASKID = \"dam_task0\"\n", "IMG_PATH = \"/mbeir_images/dam_images\"\n", "\n", "DS_ID = 14\n", "TASKID = \"fgclip_task3\"\n", "IMG_PATH = \"/mbeir_images/fgclip_images\"\n", "\n", "class Args:\n", "    def __init__(self):\n", "        # Define the environment variables from the command\n", "        _MODEL_ID = \"./checkpoints/LamRA-Ret\"\n", "        _ORIGINAL_MODEL_ID = \"Qwen/Qwen2-VL-7B-Instruct\"\n", "        _IMAGE_PATH_PREFIX = \"/mnt/tidal-alsh01/dataset/mmeb/M-BEIR\"\n", "\n", "        # Arguments passed in the command line\n", "        self.query_data_path: str = f\"{_IMAGE_PATH_PREFIX}/query/test/mbeir_{TASKID}_test.jsonl\"\n", "        self.query_cand_pool_path: str = f\"{_IMAGE_PATH_PREFIX}/cand_pool/test/mbeir_{TASKID}_cand_pool.jsonl\"\n", "        self.cand_pool_path: str = f\"{_IMAGE_PATH_PREFIX}/cand_pool/test/mbeir_{TASKID}_cand_pool.jsonl\"\n", "        self.instructions_path: str = f\"{_IMAGE_PATH_PREFIX}/instructions/query_instructions.tsv\"\n", "        self.qrels_path: str = f\"{_IMAGE_PATH_PREFIX}/qrels/test/mbeir_{TASKID}_test_qrels.txt\"\n", "        self.original_model_id: str = _ORIGINAL_MODEL_ID\n", "        self.image_path_prefix: str = _IMAGE_PATH_PREFIX + IMG_PATH\n", "        self.model_id: str = _MODEL_ID\n", "\n", "        # Argument with a default value from the argparse definition (not overridden in the command)\n", "        self.model_max_length: int = 1024"]}, {"cell_type": "markdown", "id": "2d81c621", "metadata": {}, "source": ["# xhs 评估指标验证"]}, {"cell_type": "code", "execution_count": 6, "id": "7ff290b6", "metadata": {}, "outputs": [], "source": ["args = Args()\n", "from dataset.datasets_mbeir import QueryDataset, CandidateDataset\n", "from PIL import Image\n", "cand_dataset = CandidateDataset(\n", "    query_data_path=args.query_data_path, \n", "    cand_pool_path=args.cand_pool_path,\n", "    instructions_path=args.instructions_path,\n", "    image_path_prefix=args.image_path_prefix\n", ")\n", "query_dataset = QueryDataset(\n", "    query_data_path=args.query_data_path, \n", "    cand_pool_path=args.query_cand_pool_path,\n", "    instructions_path=args.instructions_path,\n", "    image_path_prefix=args.image_path_prefix\n", ")\n"]}, {"cell_type": "code", "execution_count": 7, "id": "5cd7ab1e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Retriever: Loaded 999 queries from /mnt/tidalfs-hssh01/dataset/mmeb/M-BEIR/qrels/test/mbeir_xhs_task7_test_qrels.txt\n", "Retriever: Average number of relevant documents per query: 4.24\n"]}], "source": ["qrel, _ = load_qrel(args.qrels_path)\n", "cand_pool = {d['did']:d for d in cand_dataset.cand_pool}"]}, {"cell_type": "code", "execution_count": null, "id": "6b235fb3", "metadata": {}, "outputs": [], "source": ["def get_image_info(item_data, image_path_prefix=\"\"):\n", "    \"\"\"\n", "    统一获取图片信息的函数\n", "    Args:\n", "        item_data: 可以是query数据或candidate数据\n", "        image_path_prefix: 图片路径前缀\n", "    Returns:\n", "        tuple: (image_path, box) 如果没有图片则返回 (None, None)\n", "    \"\"\"\n", "    if isinstance(item_data, dict):\n", "        # 处理candidate数据格式\n", "        if 'img_path' in item_data:\n", "            img_path = os.path.join(image_path_prefix, item_data['img_path'])\n", "            box = item_data.get('box', None)\n", "            return img_path, box\n", "        return None, None\n", "    elif isinstance(item_data, list):\n", "        # 处理query数据格式\n", "        try:\n", "            if len(item_data) > 0 and 'content' in item_data[0]:\n", "                content = item_data[0]['content']\n", "                # 查找图片内容\n", "                for item in content:\n", "                    if 'image' in item:\n", "                        return item['image'], item.get('box', None)\n", "            return None, None\n", "        except (<PERSON><PERSON><PERSON><PERSON>, Index<PERSON><PERSON>r, TypeError):\n", "            return None, None\n", "    else:\n", "        return None, None\n", "\n", "def get_text_info(query_data):\n", "    \"\"\"\n", "    统一获取文本信息的函数\n", "    Args:\n", "        query_data: query数据\n", "    Returns:\n", "        str: 文本内容，如果没有则返回空字符串\n", "    \"\"\"\n", "    try:\n", "        if isinstance(query_data, list) and len(query_data) > 0:\n", "            content = query_data[0].get('content', [])\n", "            for item in content:\n", "                if 'text' in item:\n", "                    return item['text']\n", "        return \"\"\n", "    except (<PERSON><PERSON><PERSON><PERSON>, Index<PERSON><PERSON>r, TypeError):\n", "        return \"\"\n", "\n", "def show_items_with_images(items, image_path_prefix=\"\", title_prefix=\"\", max_items=10):\n", "    \"\"\"\n", "    统一显示带图片的items（可以是query或candidates）\n", "    Args:\n", "        items: 要显示的items列表\n", "        image_path_prefix: 图片路径前缀\n", "        title_prefix: 标题前缀\n", "        max_items: 最大显示数量\n", "    \"\"\"\n", "    if not items:\n", "        print(f\"{title_prefix}No items to show\")\n", "        return\n", "    \n", "    image_paths = []\n", "    box_list = []\n", "    txt_list = []\n", "    \n", "    display_items = items[:max_items] if len(items) > max_items else items\n", "    \n", "    for item in display_items:\n", "        try:\n", "            img_path, box = get_image_info(item, image_path_prefix)\n", "            if img_path:\n", "                image_paths.append(img_path)\n", "                box_list.append(box)\n", "            else:\n", "                print(f\"{title_prefix}Warning: No image found for item\")\n", "        except:\n", "            txt_list.append(item['txt'])\n", "\n", "    if image_paths:\n", "        print(f\"{title_prefix}Showing {len(image_paths)} images\")\n", "        show_group_imgs(image_paths, box_list)\n", "    if txt_list:\n", "        print(txt_list)\n", "\n", "def show_some_query_info(query_idx):\n", "    \"\"\"\n", "    显示query信息的统一函数\n", "    \"\"\"\n", "    query, _ = query_dataset[query_idx]\n", "    \n", "    # 获取并显示文本信息\n", "    text_content = get_text_info(query)\n", "    if text_content:\n", "        print(f\"Query text: {text_content}\")\n", "    \n", "    # 获取query图片信息\n", "    query_img_path, query_box = get_image_info(query)\n", "    \n", "    # 获取positive candidates\n", "    poscand_did_list = qrel[f'{DS_ID}:{query_idx+1}']\n", "    \n", "    # 准备显示的图片列表\n", "    all_items = []\n", "    \n", "    # 添加query（如果有图片）\n", "    if query_img_path:\n", "        print(f\"Query box: {query_box}\")\n", "        # 创建一个类似candidate格式的字典来复用get_image_info\n", "        query_item = {'img_path': query_img_path.replace(args.image_path_prefix + '/', ''), 'box': query_box}\n", "        all_items.append(query_item)\n", "    \n", "    # 添加positive candidates\n", "    for did in poscand_did_list:\n", "        if did in cand_pool:\n", "            all_items.append(cand_pool[did])\n", "    \n", "    # 显示所有图片\n", "    if all_items:\n", "        show_items_with_images(all_items, args.image_path_prefix, \"Query and positive candidates: \")\n", "    else:\n", "        print(\"No images to display for this query\")\n", "    \n", "    return query, poscand_did_list\n", "\n", "def show_candidates_results(cand_names, query_idx, title=\"Results\", max_display=10):\n", "    \"\"\"\n", "    统一显示候选结果的函数\n", "    Args:\n", "        cand_names: 候选者did列表\n", "        query_idx: query索引\n", "        title: 显示标题\n", "        max_display: 最大显示数量\n", "    \"\"\"\n", "    print(f\"\\n{title}\")\n", "    \n", "    if not cand_names:\n", "        print(\"No candidates to show\")\n", "        return\n", "    \n", "    # 获取候选者数据\n", "    candidates = []\n", "    for i, did in enumerate(cand_names[:max_display]):\n", "        if did in cand_pool:\n", "            candidates.append(cand_pool[did])\n", "        else:\n", "            print(f\"Warning: Candidate {did} not found in pool\")\n", "    \n", "    # 显示候选者图片\n", "    show_items_with_images(candidates, args.image_path_prefix, f\"{title}: \", max_display)\n", "\n", "def show_some_cand(did):\n", "    \"\"\"\n", "    显示单个候选者\n", "    \"\"\"\n", "    if did not in cand_pool:\n", "        print(f\"Candidate {did} not found\")\n", "        return None\n", "    \n", "    img_path, box = get_image_info(cand_pool[did], args.image_path_prefix)\n", "    if img_path:\n", "        return Image.open(img_path)\n", "    else:\n", "        print(f\"No image for candidate {did}\")\n", "        return None\n", "\n", "def show_group_imgs(image_paths, box_list=None, output_path=None):\n", "    \"\"\"\n", "    显示图片组的函数，增加了错误处理\n", "    \"\"\"\n", "    def draw(image_path, box):\n", "        from PIL import Image, ImageDraw\n", "        \n", "        try:\n", "            image = Image.open(image_path)\n", "        except Exception as e:\n", "            print(f\"Error opening image {image_path}: {e}\")\n", "            return None\n", "            \n", "        if box is None: \n", "            return image\n", "            \n", "        width, height = image.size\n", "        x0 = width * box[0]\n", "        y0 = height * box[1]\n", "        x1 = width * box[2]\n", "        y1 = height * box[3]\n", "\n", "\n", "        draw = ImageDraw.Draw(image)\n", "        # (x0, y0) is top-left, (x1, y1) is bottom-right\n", "        line_thickness = max(2, int(min(width, height) * 0.01))\n", "        draw.rectangle(\n", "            [(x0, y0), (x1, y1)], \n", "            outline=\"red\", \n", "            width=line_thickness\n", "        )\n", "        return image\n", "    \n", "    if not image_paths:\n", "        print(\"No images to display\")\n", "        return\n", "    \n", "    images = []\n", "    if box_list is None: \n", "        box_list = [None] * len(image_paths)\n", "    \n", "    # 确保box_list长度匹配\n", "    while len(box_list) < len(image_paths):\n", "        box_list.append(None)\n", "    \n", "    for path, box in zip(image_paths, box_list):\n", "        img = draw(path, box)\n", "        if img is not None:\n", "            images.append(img.resize((224, 224)))\n", "    \n", "    if not images:\n", "        print(\"No valid images to display\")\n", "        return\n", "    \n", "    # 获取第一张图片的模式和大小\n", "    mode = images[0].mode\n", "    width, height = images[0].size\n", "    \n", "    # 检查所有图片是否模式一致\n", "    for img in images:\n", "        if img.mode != mode:\n", "            img = img.convert(mode)\n", "    \n", "    # 计算拼接后总宽度和高度\n", "    total_width = sum(img.width for img in images)\n", "    max_height = max(img.height for img in images)\n", "    \n", "    # 创建空白画布\n", "    result = Image.new(mode, (total_width, max_height), (255, 255, 255))\n", "    \n", "    # 拼接图片\n", "    x_offset = 0\n", "    for img in images:\n", "        result.paste(img, (x_offset, 0))\n", "        x_offset += img.width\n", "    \n", "    # 显示结果\n", "    result.show()\n", "\n", "# def diff_topk_cands(ours, baseline, topk=5):\n", "#     strong, weak = [] , []\n", "#     for query_idx in range(len(query_dataset)):\n", "#         # for t in qrel[f'10:{query_idx+1}']:\n", "#         t = np.random.choice(qrel[f'10:{query_idx+1}'])\n", "#         if t in ours[query_idx][:topk] and (t not in baseline[query_idx][:topk]):\n", "#             strong.append(query_idx)\n", "#         elif t not in ours[query_idx][:topk] and (t in baseline[query_idx][:topk]):\n", "#             weak.append(query_idx)\n", "#     print(f\"strong:{len(strong)}, weak:{len(weak)}\")\n", "#     return strong, weak\n", "\n", "def diff_topk_cands(ours, baseline, topk=5):\n", "    strong, weak, mid = [] , [], []\n", "    totalq_len = 0\n", "    ourscnt, baselinecnt = 0, 0\n", "    for query_idx in range(len(query_dataset)):\n", "        cset = set(qrel[f'{DS_ID}:{query_idx+1}'])\n", "        totalq_len += len(cset)\n", "        ourset = set(ours[query_idx])\n", "        baseset = set(baseline[query_idx])\n", "        for t in cset:\n", "            extras = cset - {t}\n", "            l1 = len(ourset.intersection(extras))\n", "            l2 = len(baseset.intersection(extras))\n", "            t_in_ours = t in set(ours[query_idx][:topk+l1])\n", "            t_in_baseline = t in set(baseline[query_idx][:topk+l2])\n", "            if t_in_ours: ourscnt += 1\n", "            if t_in_baseline: baselinecnt += 1\n", "            if t_in_ours and not t_in_baseline:\n", "                strong.append(query_idx)\n", "            elif not t_in_ours and t_in_baseline:\n", "                weak.append(query_idx)\n", "            elif t_in_ours and t_in_baseline:\n", "                mid.append(query_idx)\n", "    print(f\"strong:{len(strong)}, weak:{len(weak)}, both:{len(mid)}\")\n", "    print(f\"ours acc:{ourscnt/totalq_len}, baseline acc:{baselinecnt/totalq_len}\")\n", "    return strong, weak, mid\n", "\n", "def compute_recall_at_k(relevant_docs, retrieved_indices, k):\n", "    if not relevant_docs:\n", "        return 0.0 # Return 0 if there are no relevant documents\n", "\n", "    # Get the set of indices for the top k retrieved documents\n", "    top_k_retrieved_indices_set = set(retrieved_indices[:k])\n", "\n", "    # Convert the relevant documents to a set\n", "    relevant_docs_set = set(relevant_docs)\n", "\n", "    # Check if there is an intersection between relevant docs and top k retrieved docs\n", "    # If there is, we return 1, indicating successful retrieval; otherwise, we return 0\n", "    result = []\n", "    for x in relevant_docs_set:\n", "        filtered_top_k = top_k_retrieved_indices_set - (relevant_docs_set - {x})\n", "        if x in filtered_top_k:\n", "            result.append(1.0)\n", "        else:\n", "            result.append(0.0)\n", "    return result\n"]}, {"cell_type": "code", "execution_count": null, "id": "2a5373a3", "metadata": {}, "outputs": [], "source": ["# 展示poscand\n", "# print(poscand_did_list)\n", "# show_some_cand(poscand_did_list[0])"]}, {"cell_type": "markdown", "id": "2ff82d14", "metadata": {}, "source": ["# 第一组实验，发现效果变差了  note-1k-1top-1pos-filter"]}, {"cell_type": "code", "execution_count": 24, "id": "476dca01-2cf4-4d7c-b8fd-02683d4ac7b4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["qwen2_5-vl-7b_cvp+xhs_ablation_lemuir_results.txt\n"]}], "source": ["!ls <PERSON><PERSON>_Ret_eval_results | grep qwen2_5-vl-7b_cvp+xhs_ablation_lemuir_results"]}, {"cell_type": "code", "execution_count": 25, "id": "77f40589-577e-40a6-aa9b-ee1b03653099", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/mnt/tidal-alsh01/dataset/mmeb/M-BEIR/qrels/test/mbeir_xhs_task7_test_qrels.txt\n", "recall_at_1 = 0.1854648419065597\n", "recall_at_5 = 0.6753185464841907\n", "recall_at_10 = 0.9023124115148655\n", "recall_at_50 = 0.9827748938178386\n"]}], "source": ["!cat LamRA_Ret_eval_results/qwen2_5-vl-7b_cvp+xhs_ablation_lemuir_results.txt"]}, {"cell_type": "code", "execution_count": null, "id": "aee02a3a", "metadata": {}, "outputs": [], "source": ["rootdir = \"LamRA_Ret_eval_results\"\n", "\n", "with open(f\"LamRA_Ret_eval_results/mbeir_{TASKID}_test_damret_cand_names.json\", \"r\") as f:\n", "    ourscand_names_json = json.load(f)\n", "\n", "with open(f\"LamRA_Ret_eval_results/lamraretcandel/mbeir_{TASKID}_test_damret_cand_names.json\", \"r\") as f:\n", "    baselinecand_names_json = json.load(f)    "]}, {"cell_type": "code", "execution_count": null, "id": "33c01aa4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["strong:526, weak:537, both:2436\n", "ours acc:0.6989145823501651, baseline acc:0.7015101462954224\n"]}], "source": ["strong, weak, both = diff_topk_cands(ourscand_names_json, baselinecand_names_json, topk=1)"]}, {"cell_type": "code", "execution_count": null, "id": "96d6d3f1", "metadata": {"scrolled": true}, "outputs": [], "source": ["# 展示top 5 的cands\n", "# show_some_cand(cands[4])\n", "# 如果是ours\n", "query_idx = weak[8]\n", "show_some_query_info(query_idx)\n", "\n", "print(\"our results\")\n", "ourcands = ourscand_names_json[query_idx] # len 50\n", "show_candidates_results(ourcands, query_idx, \"Our Results\", max_display=10)\n", "\n", "\n", "print(\"baseline results\")\n", "baselinecands = baselinecand_names_json[query_idx]\n", "show_candidates_results(baselinecands, query_idx, \"Baseline Results\", max_display=10)"]}, {"cell_type": "code", "execution_count": 70, "id": "5b222442-4649-4936-90c1-f9376a54c4e0", "metadata": {}, "outputs": [], "source": ["# cand_pool[ourcands[1]]['box']"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}